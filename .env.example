# Klicktape Environment Configuration
# Copy this file to .env and fill in your actual values

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Upstash Redis Configuration (for Stories Caching)
# Get these from: https://console.upstash.com/
EXPO_PUBLIC_UPSTASH_REDIS_REST_URL=your_upstash_redis_rest_url
EXPO_PUBLIC_UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_rest_token

# Socket.IO Server Configuration (for Real-time Chat)
# Update this IP address when your network changes
EXPO_PUBLIC_SOCKET_SERVER_URL=http://**************:3000

# Alternative URLs for different environments:
# For Android Emulator: http://********:3000
# For iOS Simulator: http://localhost:3000
# For Physical Device: http://YOUR_COMPUTER_IP:3000

# Server Port (used by the server)
PORT=3000

# Optional: Performance Monitoring
EXPO_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true

# Optional: Debug Mode
EXPO_PUBLIC_DEBUG_MODE=false
